<?php

namespace app\admin\model\wefinancial;

use think\Model;


class ReportItemSubject extends Model
{





    // 表名
    protected $name = 'wefinancial_report_item_subject';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'operation_type_text',
        'balance_direction_text',
        'status_text'
    ];



    public function getOperationTypeList()
    {
        return ['add' => __('Operation_type add'), 'subtract' => __('Operation_type subtract')];
    }

    public function getBalanceDirectionList()
    {
        return ['debit' => __('Balance_direction debit'), 'credit' => __('Balance_direction credit'), 'balance' => __('Balance_direction balance')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '0' => __('Status 0')];
    }


    public function getOperationTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['operation_type'] ?? '');
        $list = $this->getOperationTypeList();
        return $list[$value] ?? '';
    }


    public function getBalanceDirectionTextAttr($value, $data)
    {
        $value = $value ?: ($data['balance_direction'] ?? '');
        $list = $this->getBalanceDirectionList();
        return $list[$value] ?? '';
    }



    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }




}
