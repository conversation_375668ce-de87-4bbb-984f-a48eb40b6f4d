{php}
// 使用预渲染的模板数据（优化性能，减少内存占用）
$renderedRows = $templateData['renderedRows'];
$columnCount = count($columns);
$columnWidth = $columnCount > 0 ? (20 / $columnCount) : 10;
{/php}

<table class="table table-bordered report-table" style="margin-bottom: 0;">
  <thead>
  <tr>
    <!-- 左侧资产部分 -->
    <th style="width: 20%; text-align: center; background-color: #f8f9fa;">项目</th>
    <th style="width: 5%; text-align: center; background-color: #f8f9fa;">行次</th>

    <!-- 动态列头 - 左侧 -->
    {volist name="columns" id="column"}
    <th style="width: {$columnWidth}%; text-align: center; background-color: #f8f9fa;">{$column.column_name}</th>
    {/volist}

    <!-- 右侧负债和权益部分 -->
    <th style="width: 20%; text-align: center; background-color: #f8f9fa;">项目</th>
    <th style="width: 5%; text-align: center; background-color: #f8f9fa;">行次</th>

    <!-- 动态列头 - 右侧 -->
    {volist name="columns" id="column"}
    <th style="width: {$columnWidth}; text-align: center; background-color: #f8f9fa;">{$column.column_name}</th>
    {/volist}
  </tr>
  </thead>
  <tbody>
  {volist name="renderedRows" id="row"}
  <tr>
    <!-- 左侧资产 -->
    {if condition="!empty($row.left)"}
    <td  class="report-item-title" style="{if condition='$row.left.bold'}font-weight: bold;{/if}">
      {$row.left.indent_html}{$row.left.item_name}
      {if condition="$row.left.calculation_type == 'direct' && $row.left.line_number > 0"}
      <button type="button" class="btn btn-xs btn-info btn-subject-config btn-dialog no-print" data-url="wefinancial/report/subjectConfig?item_id={$row.left.id}"
              title="配置取数规则">
        <i class="fa fa-cog"></i>
      </button>
      {/if}
    </td>
    <td style="text-align: center;">{$row.left.line_number}</td>

    <!-- 动态列数据 - 左侧 -->
    {volist name="columns" id="column"}
    <td style="text-align: right;">{$row.left.line_number > 0 ? $row.left.columns[$column.id] : ''}</td>
    {/volist}
    {else}
    <!-- 空行 - 左侧 -->
    <td></td>
    <td></td>
    {volist name="columns" id="column"}
    <td></td>
    {/volist}
    {/if}

    <!-- 右侧负债和权益 -->
    {if condition="!empty($row.right)"}
    <td  class="report-item-title" style="{if condition='$row.right.bold'}font-weight: bold;{/if}">
      {$row.right.indent_html}{$row.right.item_name}
      {if condition="$row.right.calculation_type == 'direct' && $row.right.line_number > 0"}
      <button type="button" class="btn btn-xs btn-info btn-subject-config btn-dialog no-print" data-url="wefinancial/report/subjectConfig?item_id={$row.right.id}"
              title="配置取数规则">
        <i class="fa fa-cog"></i>
      </button>
      {/if}
    </td>
    <td style="text-align: center;">{$row.right.line_number}</td>

    <!-- 动态列数据 - 右侧 -->
    {volist name="columns" id="column"}
    <td style="text-align: right;">{$row.right.line_number > 0 ? $row.right.columns[$column.id] : ''}</td>
    {/volist}
    {else}
    <!-- 空行 - 右侧 -->
    <td></td>
    <td></td>
    {volist name="columns" id="column"}
    <td></td>
    {/volist}
    {/if}
  </tr>
  {/volist}
  </tbody>
</table>
