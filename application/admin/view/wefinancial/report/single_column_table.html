<table class="table table-bordered report-table" style="margin-bottom: 0;">
    <thead>
        <tr>
            <th style="width: 50%; text-align: center; background-color: #f8f9fa;">项目</th>
            <th style="width: 10%; text-align: center; background-color: #f8f9fa;">行次</th>

            <!-- 动态生成列头 -->
            {volist name="columns" id="column"}
            <th style="width: {php}echo count($columns) > 0 ? (40 / count($columns)) : 20{/php}%; text-align: center; background-color: #f8f9fa;">{$column.column_name}</th>
            {/volist}
        </tr>
    </thead>
    <tbody>
        {volist name="reportData" id="item"}
        <tr>
            <td class="report-item-title" style="{if condition='$item.bold'}font-weight: bold;{/if}">
                {php}echo str_repeat('&nbsp;', $item['indent'] * 4);{/php}{$item.item_name}
                {if condition="$item.line_number && $item.calculation_type == 'direct'"}
                <button type="button" class="btn btn-xs btn-info btn-subject-config btn-dialog no-print" data-url="wefinancial/report/subjectConfig?item_id={$item.id}" title="配置取数规则">
                    <i class="fa fa-cog"></i>
                </button>
                {/if}
            </td>
            <td style="text-align: center;">{$item.line_number|default=''}</td>

            <!-- 动态生成数据列 -->
            {volist name="columns" id="column"}
            {php}
            $columnKey = 'column_' . $column['id'];
            $amount = isset($item[$columnKey]) ? $item[$columnKey] : 0;
            $formattedAmount = $item['line_number'] >0 ? number_format($amount, $column['decimal_places']) : '';
            {/php}
            <td style="text-align: right;">{$formattedAmount}</td>
            {/volist}
        </tr>
        {/volist}
    </tbody>
</table>
